import pandas as pd
import sys
import os

def clean_whitespace(df, columns):
    """
    지정된 컬럼들의 공백을 제거하고 숫자 형태를 정규화합니다.
    """
    for col in columns:
        if col in df.columns:
            # 숫자를 문자열로 변환하면서 소수점 제거
            df[col] = df[col].astype(str).str.replace('.0', '', regex=False).str.strip()
    return df

def find_unmatched_data(file_a_path, file_b_path):
    """
    두 Excel 파일에서 매칭되지 않은 데이터를 찾습니다.
    
    Args:
        file_a_path (str): A.xlsx 파일 경로
        file_b_path (str): B.xlsx 파일 경로
    """
    
    try:
        # Excel 파일 읽기 - A.xlsx는 두 번째 행이 헤더 (첫 번째 행에 실제 컬럼명)
        print(f"파일 읽는 중: {file_a_path}")
        df_a = pd.read_excel(file_a_path, header=1)  # 두 번째 행을 헤더로 사용

        # B.xlsx는 두 번째 행이 헤더 (첫 번째 행은 빈 행)
        print(f"파일 읽는 중: {file_b_path}")
        df_b = pd.read_excel(file_b_path, header=1)  # 두 번째 행을 헤더로 사용
        
        # 컬럼명 정규화 (소문자로 변환하고 공백 제거)
        df_a.columns = [str(col).lower().strip() for col in df_a.columns]
        df_b.columns = [str(col).lower().strip() for col in df_b.columns]
        
        print(f"\n정규화된 A.xlsx 컬럼: {list(df_a.columns)}")
        print(f"정규화된 B.xlsx 컬럼: {list(df_b.columns)}")
        
        # 필요한 컬럼 확인
        required_columns = ['externalskuid', 'vendoritemid']
        
        # 필요한 컬럼이 있는지 확인
        missing_cols_a = [col for col in required_columns if col not in df_a.columns]
        missing_cols_b = [col for col in required_columns if col not in df_b.columns]
        
        if missing_cols_a:
            print(f"경고: A.xlsx에서 누락된 컬럼: {missing_cols_a}")
            return None
        if missing_cols_b:
            print(f"경고: B.xlsx에서 누락된 컬럼: {missing_cols_b}")
            return None
        
        # 공백 제거
        print("\n공백 제거 중...")
        df_a = clean_whitespace(df_a, required_columns)
        df_b = clean_whitespace(df_b, required_columns)
        
        # 매칭을 위한 키 생성 (externalskuid + vendoritemid)
        df_a['match_key'] = df_a['externalskuid'].astype(str) + '|' + df_a['vendoritemid'].astype(str)
        df_b['match_key'] = df_b['externalskuid'].astype(str) + '|' + df_b['vendoritemid'].astype(str)
        
        print("\n매칭되지 않은 데이터 찾는 중...")
        
        # A.xlsx에만 있는 데이터 (B.xlsx에 없는 데이터)
        unmatched_a = df_a[~df_a['match_key'].isin(df_b['match_key'])].copy()
        unmatched_a = unmatched_a.drop('match_key', axis=1)
        
        # B.xlsx에만 있는 데이터 (A.xlsx에 없는 데이터)
        unmatched_b = df_b[~df_b['match_key'].isin(df_a['match_key'])].copy()
        unmatched_b = unmatched_b.drop('match_key', axis=1)
        
        print(f"\n매칭되지 않은 데이터 결과:")
        print(f"A.xlsx 총 행 수: {len(df_a)}")
        print(f"B.xlsx 총 행 수: {len(df_b)}")
        print(f"A.xlsx에만 있는 데이터: {len(unmatched_a)}행")
        print(f"B.xlsx에만 있는 데이터: {len(unmatched_b)}행")
        
        # A.xlsx에만 있는 데이터 저장
        if len(unmatched_a) > 0:
            output_a = "unmatched_A_only.xlsx"
            unmatched_a.to_excel(output_a, index=False)
            print(f"\nA.xlsx에만 있는 데이터가 {output_a}에 저장되었습니다.")
            
            # CSV로도 저장
            csv_a = "unmatched_A_only.csv"
            unmatched_a.to_csv(csv_a, index=False, encoding='utf-8-sig')
            print(f"CSV 형태로도 {csv_a}에 저장되었습니다.")
            
            print(f"\nA.xlsx에만 있는 데이터 미리보기 (처음 5행):")
            print(unmatched_a[['externalskuid', 'vendoritemid']].head().to_string())
        else:
            print("\nA.xlsx에만 있는 데이터가 없습니다.")
        
        # B.xlsx에만 있는 데이터 저장
        if len(unmatched_b) > 0:
            output_b = "unmatched_B_only.xlsx"
            unmatched_b.to_excel(output_b, index=False)
            print(f"\nB.xlsx에만 있는 데이터가 {output_b}에 저장되었습니다.")
            
            # CSV로도 저장
            csv_b = "unmatched_B_only.csv"
            unmatched_b.to_csv(csv_b, index=False, encoding='utf-8-sig')
            print(f"CSV 형태로도 {csv_b}에 저장되었습니다.")
            
            print(f"\nB.xlsx에만 있는 데이터 미리보기 (처음 5행):")
            print(unmatched_b[['externalskuid', 'vendoritemid']].head().to_string())
        else:
            print("\nB.xlsx에만 있는 데이터가 없습니다.")
        
        # 전체 요약 정보
        total_unique_a = len(df_a)
        total_unique_b = len(df_b)
        matched_count = total_unique_a - len(unmatched_a)  # A 기준으로 매칭된 개수
        
        print(f"\n" + "="*60)
        print(f"전체 요약:")
        print(f"  A.xlsx 전체 데이터: {total_unique_a:,}행")
        print(f"  B.xlsx 전체 데이터: {total_unique_b:,}행")
        print(f"  매칭된 데이터: {matched_count:,}행")
        print(f"  A.xlsx에만 있는 데이터: {len(unmatched_a):,}행")
        print(f"  B.xlsx에만 있는 데이터: {len(unmatched_b):,}행")
        print(f"="*60)
        
        return unmatched_a, unmatched_b
        
    except FileNotFoundError as e:
        print(f"파일을 찾을 수 없습니다: {e}")
        return None, None
    except Exception as e:
        print(f"오류가 발생했습니다: {e}")
        return None, None

def main():
    """
    메인 함수
    """
    # 파일 경로 설정
    file_a = "A.xlsx"
    file_b = "B.xlsx"
    
    # 파일 존재 확인
    if not os.path.exists(file_a):
        print(f"오류: {file_a} 파일이 존재하지 않습니다.")
        return
    
    if not os.path.exists(file_b):
        print(f"오류: {file_b} 파일이 존재하지 않습니다.")
        return
    
    print("매칭되지 않은 데이터 찾기 프로그램을 시작합니다...")
    print("=" * 60)
    
    # 매칭되지 않은 데이터 찾기 실행
    unmatched_a, unmatched_b = find_unmatched_data(file_a, file_b)
    
    if unmatched_a is not None and unmatched_b is not None:
        print("\n프로그램이 성공적으로 완료되었습니다!")
    else:
        print("\n프로그램 실행 중 오류가 발생했습니다.")

if __name__ == "__main__":
    main()
